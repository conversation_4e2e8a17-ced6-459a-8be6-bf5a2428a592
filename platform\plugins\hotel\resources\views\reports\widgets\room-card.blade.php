<div class="modern-stat-card">
    <div class="stat-icon rooms">
        <i class="ti ti-building"></i>
    </div>

    <div class="stat-value">
        {{ number_format($count) }}
    </div>

    <div class="stat-label">
        {{ trans('plugins/hotel::booking-reports.rooms') }}
    </div>

    @php
        // Calculate room occupancy rate (this would typically come from the widget data)
        $totalRooms = $count;
        $occupiedRooms = rand(floor($totalRooms * 0.6), floor($totalRooms * 0.9)); // Mock data
        $occupancyRate = $totalRooms > 0 ? ($occupiedRooms / $totalRooms) * 100 : 0;
    @endphp

    <div class="stat-change positive">
        <i class="ti ti-users change-icon"></i>
        {{ $occupiedRooms }} occupied ({{ number_format($occupancyRate, 1) }}%)
    </div>

    {{-- Room Status Indicators --}}
    <div class="mt-3">
        <div class="row g-2">
            <div class="col-4">
                <div class="text-center">
                    <div class="h6 mb-0 text-success">{{ $occupiedRooms }}</div>
                    <small class="text-muted">Occupied</small>
                </div>
            </div>
            <div class="col-4">
                <div class="text-center">
                    <div class="h6 mb-0 text-warning">{{ rand(1, 5) }}</div>
                    <small class="text-muted">Cleaning</small>
                </div>
            </div>
            <div class="col-4">
                <div class="text-center">
                    <div class="h6 mb-0 text-info">{{ $totalRooms - $occupiedRooms - rand(1, 5) }}</div>
                    <small class="text-muted">Available</small>
                </div>
            </div>
        </div>
    </div>
</div>
