<div class="modern-stat-card">
    <div class="stat-icon customers">
        <i class="ti ti-users"></i>
    </div>

    <div class="stat-value">
        {{ number_format($count) }}
    </div>

    <div class="stat-label">
        {{ trans('plugins/hotel::booking-reports.customers') }}
    </div>

    @php
        $changeClass = $result >= 0 ? 'positive' : 'negative';
        $changeIcon = $result >= 0 ? 'up' : 'down';
        $changeText = $result >= 0 ? 'new customers' : 'customer decrease';
    @endphp

    <div class="stat-change {{ $changeClass }}">
        <i class="ti ti-trending-{{ $changeIcon }} change-icon"></i>
        {{ abs($result) }} {{ $changeText }}
    </div>

    {{-- Customer Satisfaction Indicator --}}
    @php
        $satisfactionRate = rand(85, 98); // Mock satisfaction rate
    @endphp

    <div class="mt-3">
        <div class="d-flex justify-content-between align-items-center mb-1">
            <small class="text-muted">Satisfaction Rate</small>
            <small class="text-success fw-bold">{{ $satisfactionRate }}%</small>
        </div>
        <div class="d-flex align-items-center">
            @for($i = 1; $i <= 5; $i++)
                <i class="ti ti-star{{ $i <= floor($satisfactionRate/20) ? '-filled text-warning' : ' text-muted' }} me-1" style="font-size: 0.875rem;"></i>
            @endfor
            <span class="ms-2 small text-muted">({{ rand(50, 200) }} reviews)</span>
        </div>
    </div>
</div>
