<div class="modern-stat-card">
    <div class="stat-icon bookings">
        <i class="ti ti-calendar-check"></i>
    </div>

    <div class="stat-value">
        {{ number_format($count) }}
    </div>

    <div class="stat-label">
        {{ trans('plugins/hotel::booking-reports.bookings') }}
    </div>

    @php
        $changeClass = $result >= 0 ? 'positive' : 'negative';
        $changeIcon = $result >= 0 ? 'up' : 'down';
        $changeText = $result >= 0 ? 'increase' : 'decrease';
    @endphp

    <div class="stat-change {{ $changeClass }}">
        <i class="ti ti-trending-{{ $changeIcon }} change-icon"></i>
        {{ abs($result) }} bookings {{ $changeText }}
    </div>

    {{-- Progress indicator for monthly target --}}
    @php
        $monthlyTarget = 100; // This could come from settings
        $progress = min(($count / $monthlyTarget) * 100, 100);
    @endphp

    <div class="mt-3">
        <div class="d-flex justify-content-between align-items-center mb-1">
            <small class="text-muted">Monthly Target</small>
            <small class="text-muted">{{ number_format($progress, 1) }}%</small>
        </div>
        <div class="progress" style="height: 6px;">
            <div class="progress-bar bg-gradient"
                 style="width: {{ $progress }}%; background: linear-gradient(90deg, #f093fb, #f5576c);"
                 role="progressbar"></div>
        </div>
    </div>
</div>
