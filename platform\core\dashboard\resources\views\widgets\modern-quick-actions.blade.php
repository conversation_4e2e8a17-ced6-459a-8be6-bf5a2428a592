<div class="chart-widget">
    <div class="chart-header">
        <h3>Quick Actions</h3>
        <span class="chart-period">Shortcuts</span>
    </div>
    
    <div class="quick-actions-grid">
        @php
            $quickActions = [
                [
                    'title' => 'New Booking',
                    'description' => 'Create a new reservation',
                    'icon' => 'ti ti-calendar-plus',
                    'color' => 'primary',
                    'url' => route('bookings.create', [], false) ?? '#'
                ],
                [
                    'title' => 'Add Room',
                    'description' => 'Register new room',
                    'icon' => 'ti ti-building-plus',
                    'color' => 'success',
                    'url' => route('rooms.create', [], false) ?? '#'
                ],
                [
                    'title' => 'Customer List',
                    'description' => 'Manage customers',
                    'icon' => 'ti ti-users',
                    'color' => 'info',
                    'url' => route('customers.index', [], false) ?? '#'
                ],
                [
                    'title' => 'Reports',
                    'description' => 'View analytics',
                    'icon' => 'ti ti-chart-bar',
                    'color' => 'warning',
                    'url' => route('reports.index', [], false) ?? '#'
                ],
                [
                    'title' => 'Settings',
                    'description' => 'System configuration',
                    'icon' => 'ti ti-settings',
                    'color' => 'secondary',
                    'url' => route('settings.index', [], false) ?? '#'
                ],
                [
                    'title' => 'Maintenance',
                    'description' => 'Room maintenance',
                    'icon' => 'ti ti-tools',
                    'color' => 'danger',
                    'url' => '#'
                ]
            ];
        @endphp
        
        @foreach($quickActions as $action)
            <a href="{{ $action['url'] }}" class="quick-action-item">
                <div class="quick-action-icon bg-{{ $action['color'] }}">
                    <i class="{{ $action['icon'] }}"></i>
                </div>
                <div class="quick-action-content">
                    <div class="quick-action-title">{{ $action['title'] }}</div>
                    <div class="quick-action-description">{{ $action['description'] }}</div>
                </div>
                <div class="quick-action-arrow">
                    <i class="ti ti-chevron-right"></i>
                </div>
            </a>
        @endforeach
    </div>
</div>

<style>
.quick-actions-grid {
    display: grid;
    gap: 0.75rem;
}

.quick-action-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 12px;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
    
    &:hover {
        background: #f1f5f9;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        text-decoration: none;
        color: inherit;
    }
}

.quick-action-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    color: white;
    font-size: 1.1rem;
}

.quick-action-content {
    flex: 1;
}

.quick-action-title {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.quick-action-description {
    font-size: 0.875rem;
    color: #718096;
}

.quick-action-arrow {
    color: #a0aec0;
    font-size: 1.1rem;
}
</style>
