<div class="chart-widget">
    <div class="chart-header">
        <h3>Revenue Overview</h3>
        <select class="chart-period">
            <option value="7">Last 7 days</option>
            <option value="30" selected>Last 30 days</option>
            <option value="90">Last 90 days</option>
        </select>
    </div>
    
    <div class="revenue-summary mb-4">
        <div class="row g-3">
            <div class="col-6">
                <div class="revenue-metric">
                    <div class="metric-value">$24,580</div>
                    <div class="metric-label">Total Revenue</div>
                    <div class="metric-change positive">
                        <i class="ti ti-trending-up"></i>
                        +12.5%
                    </div>
                </div>
            </div>
            <div class="col-6">
                <div class="revenue-metric">
                    <div class="metric-value">$819</div>
                    <div class="metric-label">Avg. Daily</div>
                    <div class="metric-change positive">
                        <i class="ti ti-trending-up"></i>
                        +8.3%
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="chart-container">
        <canvas id="revenue-chart" height="300"></canvas>
    </div>
</div>

<style>
.revenue-summary {
    padding: 1rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.revenue-metric {
    text-align: center;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.metric-label {
    font-size: 0.875rem;
    color: #718096;
    margin-bottom: 0.5rem;
}

.metric-change {
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &.positive {
        color: #38a169;
    }
    
    &.negative {
        color: #e53e3e;
    }
    
    i {
        margin-right: 0.25rem;
    }
}

.chart-container {
    position: relative;
    height: 300px;
}
</style>

@push('footer')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('revenue-chart');
    if (ctx) {
        // Sample data - in real implementation, this would come from the backend
        const data = {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [{
                label: 'Revenue',
                data: [12000, 15000, 18000, 16000, 22000, 25000, 24000, 28000, 26000, 30000, 32000, 35000],
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#667eea',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8,
            }]
        };

        new Chart(ctx, {
            type: 'line',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(45, 55, 72, 0.9)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#667eea',
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: false,
                        callbacks: {
                            label: function(context) {
                                return '$' + context.parsed.y.toLocaleString();
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#718096'
                        }
                    },
                    y: {
                        grid: {
                            color: 'rgba(113, 128, 150, 0.1)'
                        },
                        ticks: {
                            color: '#718096',
                            callback: function(value) {
                                return '$' + (value / 1000) + 'k';
                            }
                        }
                    }
                },
                elements: {
                    point: {
                        hoverBackgroundColor: '#667eea'
                    }
                }
            }
        });
    }
});
</script>
@endpush
