<div class="activity-feed">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h3 class="mb-0">Recent Activity</h3>
        <a href="#" class="btn btn-sm btn-outline-primary">View All</a>
    </div>
    
    <div class="activity-list">
        @php
            $activities = [
                [
                    'icon' => 'ti ti-calendar-check',
                    'color' => 'bg-success',
                    'title' => 'New Booking Confirmed',
                    'description' => 'Room 205 booked by <PERSON> for 3 nights',
                    'time' => '2 minutes ago'
                ],
                [
                    'icon' => 'ti ti-currency-dollar',
                    'color' => 'bg-primary',
                    'title' => 'Payment Received',
                    'description' => '$450 payment processed for booking #1234',
                    'time' => '15 minutes ago'
                ],
                [
                    'icon' => 'ti ti-user-plus',
                    'color' => 'bg-info',
                    'title' => 'New Customer Registration',
                    'description' => '<PERSON> created a new account',
                    'time' => '1 hour ago'
                ],
                [
                    'icon' => 'ti ti-star',
                    'color' => 'bg-warning',
                    'title' => 'New Review',
                    'description' => '5-star review received from <PERSON>',
                    'time' => '2 hours ago'
                ],
                [
                    'icon' => 'ti ti-tools',
                    'color' => 'bg-secondary',
                    'title' => 'Maintenance Completed',
                    'description' => 'Room 301 AC repair completed',
                    'time' => '3 hours ago'
                ],
                [
                    'icon' => 'ti ti-calendar-x',
                    'color' => 'bg-danger',
                    'title' => 'Booking Cancelled',
                    'description' => 'Booking #1230 cancelled by guest',
                    'time' => '4 hours ago'
                ]
            ];
        @endphp
        
        @foreach($activities as $activity)
            <div class="activity-item">
                <div class="activity-icon {{ $activity['color'] }}">
                    <i class="{{ $activity['icon'] }}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">{{ $activity['title'] }}</div>
                    <div class="activity-description">{{ $activity['description'] }}</div>
                    <div class="activity-time">{{ $activity['time'] }}</div>
                </div>
            </div>
        @endforeach
    </div>
</div>
