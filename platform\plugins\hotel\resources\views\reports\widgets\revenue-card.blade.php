<div class="modern-stat-card">
    <div class="stat-icon revenue">
        <i class="ti ti-currency-dollar"></i>
    </div>

    <div class="stat-value">
        {{ format_price(Arr::get($revenue, 'revenue')) }}
    </div>

    <div class="stat-label">
        {{ trans('plugins/hotel::booking-reports.revenue') }}
    </div>

    @php
        $currentRevenue = Arr::get($revenue, 'revenue', 0);
        $previousRevenue = Arr::get($revenue, 'previous_revenue', 0);
        $change = $previousRevenue > 0 ? (($currentRevenue - $previousRevenue) / $previousRevenue) * 100 : 0;
        $isPositive = $change >= 0;
    @endphp

    <div class="stat-change {{ $isPositive ? 'positive' : 'negative' }}">
        <i class="ti ti-trending-{{ $isPositive ? 'up' : 'down' }} change-icon"></i>
        {{ number_format(abs($change), 1) }}% {{ $isPositive ? 'increase' : 'decrease' }}
    </div>

    {{-- Mini Chart --}}
    <div class="mt-3">
        <canvas id="revenue-mini-chart" width="100" height="30"></canvas>
    </div>
</div>

@push('footer')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('revenue-mini-chart');
    if (ctx) {
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: @json(array_keys(Arr::get($revenue, 'chart_data', []))),
                datasets: [{
                    data: @json(array_values(Arr::get($revenue, 'chart_data', []))),
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4,
                    pointRadius: 0,
                    pointHoverRadius: 4,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                    tooltip: { enabled: false }
                },
                scales: {
                    x: { display: false },
                    y: { display: false }
                },
                elements: {
                    point: { radius: 0 }
                }
            }
        });
    }
});
</script>
@endpush
