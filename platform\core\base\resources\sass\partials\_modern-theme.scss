/* Modern Theme Variables */
:root {
    /* Primary Colors */
    --modern-primary: #667eea;
    --modern-primary-dark: #5a67d8;
    --modern-primary-light: #7c3aed;
    
    /* Secondary Colors */
    --modern-secondary: #764ba2;
    --modern-accent: #f093fb;
    --modern-accent-alt: #f5576c;
    
    /* Neutral Colors */
    --modern-gray-50: #f9fafb;
    --modern-gray-100: #f3f4f6;
    --modern-gray-200: #e5e7eb;
    --modern-gray-300: #d1d5db;
    --modern-gray-400: #9ca3af;
    --modern-gray-500: #6b7280;
    --modern-gray-600: #4b5563;
    --modern-gray-700: #374151;
    --modern-gray-800: #1f2937;
    --modern-gray-900: #111827;
    
    /* Status Colors */
    --modern-success: #10b981;
    --modern-warning: #f59e0b;
    --modern-danger: #ef4444;
    --modern-info: #3b82f6;
    
    /* Background Gradients */
    --modern-gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --modern-gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --modern-gradient-success: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --modern-gradient-info: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    
    /* Typography */
    --modern-font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --modern-font-size-xs: 0.75rem;
    --modern-font-size-sm: 0.875rem;
    --modern-font-size-base: 1rem;
    --modern-font-size-lg: 1.125rem;
    --modern-font-size-xl: 1.25rem;
    --modern-font-size-2xl: 1.5rem;
    --modern-font-size-3xl: 1.875rem;
    --modern-font-size-4xl: 2.25rem;
    
    /* Font Weights */
    --modern-font-weight-light: 300;
    --modern-font-weight-normal: 400;
    --modern-font-weight-medium: 500;
    --modern-font-weight-semibold: 600;
    --modern-font-weight-bold: 700;
    --modern-font-weight-extrabold: 800;
    
    /* Spacing */
    --modern-spacing-xs: 0.25rem;
    --modern-spacing-sm: 0.5rem;
    --modern-spacing-md: 1rem;
    --modern-spacing-lg: 1.5rem;
    --modern-spacing-xl: 2rem;
    --modern-spacing-2xl: 3rem;
    
    /* Border Radius */
    --modern-radius-sm: 0.375rem;
    --modern-radius-md: 0.5rem;
    --modern-radius-lg: 0.75rem;
    --modern-radius-xl: 1rem;
    --modern-radius-2xl: 1.5rem;
    
    /* Shadows */
    --modern-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --modern-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --modern-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --modern-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --modern-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Modern Typography */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

body {
    font-family: var(--modern-font-family);
    font-size: var(--modern-font-size-base);
    line-height: 1.6;
    color: var(--modern-gray-700);
}

/* Headings */
h1, .h1 {
    font-size: var(--modern-font-size-4xl);
    font-weight: var(--modern-font-weight-bold);
    line-height: 1.2;
    color: var(--modern-gray-900);
    margin-bottom: var(--modern-spacing-lg);
}

h2, .h2 {
    font-size: var(--modern-font-size-3xl);
    font-weight: var(--modern-font-weight-semibold);
    line-height: 1.3;
    color: var(--modern-gray-800);
    margin-bottom: var(--modern-spacing-md);
}

h3, .h3 {
    font-size: var(--modern-font-size-2xl);
    font-weight: var(--modern-font-weight-semibold);
    line-height: 1.4;
    color: var(--modern-gray-800);
    margin-bottom: var(--modern-spacing-md);
}

h4, .h4 {
    font-size: var(--modern-font-size-xl);
    font-weight: var(--modern-font-weight-medium);
    line-height: 1.4;
    color: var(--modern-gray-700);
    margin-bottom: var(--modern-spacing-sm);
}

h5, .h5 {
    font-size: var(--modern-font-size-lg);
    font-weight: var(--modern-font-weight-medium);
    line-height: 1.5;
    color: var(--modern-gray-700);
    margin-bottom: var(--modern-spacing-sm);
}

h6, .h6 {
    font-size: var(--modern-font-size-base);
    font-weight: var(--modern-font-weight-medium);
    line-height: 1.5;
    color: var(--modern-gray-600);
    margin-bottom: var(--modern-spacing-sm);
}

/* Text Utilities */
.text-modern-primary { color: var(--modern-primary) !important; }
.text-modern-secondary { color: var(--modern-secondary) !important; }
.text-modern-success { color: var(--modern-success) !important; }
.text-modern-warning { color: var(--modern-warning) !important; }
.text-modern-danger { color: var(--modern-danger) !important; }
.text-modern-info { color: var(--modern-info) !important; }

/* Background Utilities */
.bg-modern-primary { background-color: var(--modern-primary) !important; }
.bg-modern-secondary { background-color: var(--modern-secondary) !important; }
.bg-modern-gradient-primary { background: var(--modern-gradient-primary) !important; }
.bg-modern-gradient-secondary { background: var(--modern-gradient-secondary) !important; }
.bg-modern-gradient-success { background: var(--modern-gradient-success) !important; }
.bg-modern-gradient-info { background: var(--modern-gradient-info) !important; }

/* Modern Button Styles */
.btn-modern {
    font-family: var(--modern-font-family);
    font-weight: var(--modern-font-weight-medium);
    border-radius: var(--modern-radius-lg);
    padding: var(--modern-spacing-sm) var(--modern-spacing-lg);
    transition: all 0.3s ease;
    border: none;
    
    &:hover {
        transform: translateY(-2px);
        box-shadow: var(--modern-shadow-lg);
    }
    
    &.btn-modern-primary {
        background: var(--modern-gradient-primary);
        color: white;
        
        &:hover {
            background: var(--modern-gradient-primary);
            filter: brightness(1.1);
        }
    }
    
    &.btn-modern-secondary {
        background: var(--modern-gradient-secondary);
        color: white;
        
        &:hover {
            background: var(--modern-gradient-secondary);
            filter: brightness(1.1);
        }
    }
}

/* Modern Card Styles */
.card-modern {
    border: none;
    border-radius: var(--modern-radius-2xl);
    box-shadow: var(--modern-shadow-md);
    transition: all 0.3s ease;
    
    &:hover {
        transform: translateY(-4px);
        box-shadow: var(--modern-shadow-xl);
    }
    
    .card-header {
        background: transparent;
        border-bottom: 1px solid var(--modern-gray-200);
        padding: var(--modern-spacing-lg);
        
        .card-title {
            font-weight: var(--modern-font-weight-semibold);
            color: var(--modern-gray-800);
            margin: 0;
        }
    }
    
    .card-body {
        padding: var(--modern-spacing-lg);
    }
}

/* Modern Form Styles */
.form-control-modern {
    border: 2px solid var(--modern-gray-200);
    border-radius: var(--modern-radius-lg);
    padding: var(--modern-spacing-sm) var(--modern-spacing-md);
    font-family: var(--modern-font-family);
    transition: all 0.3s ease;
    
    &:focus {
        border-color: var(--modern-primary);
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        outline: none;
    }
}

/* Modern Alert Styles */
.alert-modern {
    border: none;
    border-radius: var(--modern-radius-xl);
    padding: var(--modern-spacing-lg);
    font-family: var(--modern-font-family);
    
    &.alert-modern-success {
        background: linear-gradient(135deg, rgba(67, 233, 123, 0.1), rgba(56, 249, 215, 0.1));
        color: var(--modern-success);
        border-left: 4px solid var(--modern-success);
    }
    
    &.alert-modern-warning {
        background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(251, 191, 36, 0.1));
        color: var(--modern-warning);
        border-left: 4px solid var(--modern-warning);
    }
    
    &.alert-modern-danger {
        background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(248, 113, 113, 0.1));
        color: var(--modern-danger);
        border-left: 4px solid var(--modern-danger);
    }
    
    &.alert-modern-info {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 197, 253, 0.1));
        color: var(--modern-info);
        border-left: 4px solid var(--modern-info);
    }
}
