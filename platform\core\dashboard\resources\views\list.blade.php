@extends(BaseHelper::getAdminMasterLayoutTemplate())

@push('header-action')
    @if (count($widgets) > 0)
        <x-core::button
            color="primary"
            :outlined="true"
            class="manage-widget"
            data-bs-toggle="modal"
            data-bs-target="#widgets-management-modal"
            icon="ti ti-layout-dashboard"
        >
            {{ trans('core/dashboard::dashboard.manage_widgets') }}
        </x-core::button>
    @endif
@endpush

@section('content')
    {{-- Modern Dashboard Header --}}
    <div class="dashboard-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1>{{ trans('core/dashboard::dashboard.welcome_back') }}, {{ Auth::user()->name }}!</h1>
                <p class="dashboard-subtitle">{{ trans('core/dashboard::dashboard.here_is_overview') }}</p>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="d-flex align-items-center justify-content-md-end">
                    <div class="me-3">
                        <small class="text-white-50">{{ trans('core/dashboard::dashboard.last_login') }}</small>
                        <div class="text-white">{{ Auth::user()->last_login_at ? Auth::user()->last_login_at->format('M d, Y H:i') : 'N/A' }}</div>
                    </div>
                    <div class="avatar avatar-lg">
                        <img src="{{ Auth::user()->avatar_url }}" alt="{{ Auth::user()->name }}" class="rounded-circle">
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- System Updates Alert --}}
    @if (config('core.base.general.enable_system_updater') && Auth::user()->isSuperUser())
        <v-check-for-updates
            check-update-url="{{ route('system.check-update') }}"
            v-slot="{ hasNewVersion, message }"
            v-cloak
        >
            <x-core::alert
                v-if="hasNewVersion"
                type="warning"
                class="mb-4"
            >
                @{{ message }}, please go to <a
                    href="{{ route('system.updater') }}"
                    class="text-warning fw-bold"
                >System Updater</a> to upgrade to the latest version!
            </x-core::alert>
        </v-check-for-updates>
    @endif

    {{-- Admin Notifications --}}
    <div class="mb-4">
        {!! apply_filters(DASHBOARD_FILTER_ADMIN_NOTIFICATIONS, null) !!}
    </div>

    {{-- Modern Stats Grid --}}
    <div class="modern-stats-grid">
        @foreach ($statWidgets as $widget)
            {!! $widget !!}
        @endforeach
    </div>

    {{-- Top Blocks --}}
    <div class="mb-4">
        {!! apply_filters(DASHBOARD_FILTER_TOP_BLOCKS, null) !!}
    </div>

    {{-- Modern Widget Grid --}}
    <div class="modern-widget-grid">
        {{-- Revenue Chart --}}
        <div class="widget-item">
            @include('core/dashboard::widgets.modern-revenue-chart')
        </div>

        {{-- Activity Feed --}}
        <div class="widget-item">
            @include('core/dashboard::widgets.modern-activity-feed')
        </div>

        {{-- Quick Actions --}}
        <div class="widget-item">
            @include('core/dashboard::widgets.modern-quick-actions')
        </div>

        {{-- Existing User Widgets --}}
        <div
            id="list_widgets"
            class="contents"
            data-bb-toggle="widgets-list"
            data-url="{{ route('dashboard.update_widget_order') }}"
        >
            @foreach ($userWidgets as $widget)
                <div class="widget-item">
                    {!! $widget !!}
                </div>
            @endforeach
        </div>
    </div>
@endsection

@push('footer')
    @include('core/dashboard::partials.modals', compact('widgets'))
@endpush
